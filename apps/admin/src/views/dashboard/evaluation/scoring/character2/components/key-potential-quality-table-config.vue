<template>
    <div class="key-potential-quality-table-config">
        <div class="config-header">
            <h6>关键潜在素质配置表</h6>
            <b-button type="primary" size="small" @click="addColumn">
                <template #icon>
                    <b-icon name="plus" />
                </template>
                添加表头
            </b-button>
        </div>

        <div class="config-table-wrapper">
            <b-table :tableData="tableDisplayData" :columns="dynamicColumns" border tableLayoutFixed :scroll="{ x: '100%', y: '400px' }">
                <!-- 动态生成可编辑单元格 -->
                <template v-for="(item, index) in modelValue" :key="index" #[`td-${item.headName}`]="{ raw }">
                    <div class="editable-cell">
                        <div class="cell-group">
                            <label>定义:</label>
                            <b-input v-model="raw[item.headName].define" placeholder="请输入定义" size="small" @change="updateData" />
                        </div>
                        <div class="cell-group">
                            <label>权重:</label>
                            <b-input-number
                                v-model="raw[item.headName].weight"
                                placeholder="0.0-1.0"
                                size="small"
                                :min="0"
                                :max="1"
                                :precision="2"
                                :step="0.01"
                                @change="validateWeight(item.headName)"
                            />
                        </div>
                        <div class="cell-group">
                            <label>常模平均分:</label>
                            <b-input-number
                                v-model="raw[item.headName].normalAverageScore"
                                placeholder="请输入平均分"
                                size="small"
                                :min="0"
                                :max="100"
                                :precision="1"
                                @change="updateData"
                            />
                        </div>
                        <div class="cell-group">
                            <label>常模标准差:</label>
                            <b-input-number
                                v-model="raw[item.headName].normalStandardDeviation"
                                placeholder="请输入标准差"
                                size="small"
                                :min="0"
                                :max="50"
                                :precision="2"
                                @change="updateData"
                            />
                        </div>
                    </div>
                </template>

                <!-- 操作列 -->
                <template #td-operation="{ raw }">
                    <b-action @click="removeColumn(raw.headName)">删除表头</b-action>
                </template>
            </b-table>
        </div>

        <!-- 权重总和验证提示 -->
        <div v-if="weightValidationErrors.length > 0" class="validation-errors">
            <div v-for="error in weightValidationErrors" :key="error" class="error-item">
                <b-icon name="warning" color="red" />
                {{ error }}
            </div>
        </div>

        <!-- 添加表头弹窗 -->
        <b-modal v-model="showAddHeaderDialog" title="添加表头" width="400px" @confirm="confirmAddHeader" @cancel="cancelAddHeader">
            <b-form ref="headerFormRef" :model="headerForm" labelAlign="left">
                <b-form-item
                    label="表头名称"
                    field="name"
                    asteriskPosition="end"
                    :rules="[{ required: true, message: '请输入表头名称' }, { max: 20, message: '表头名称不能超过20个字符' }, { validator: validateHeaderName }]"
                >
                    <b-input v-model="headerForm.name" placeholder="请输入表头名称" />
                </b-form-item>
            </b-form>
        </b-modal>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import type { KeyPotentialQualityItem } from '../enterprise-model';

// 全局变量声明
declare const Toast: {
    success: (message: string) => void;
    danger: (message: string) => void;
};

interface Props {
    modelValue?: KeyPotentialQualityItem[];
}

interface Emits {
    (e: 'update:modelValue', value: KeyPotentialQualityItem[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 表头管理
const showAddHeaderDialog = ref(false);
const headerForm = ref({ name: '' });
const headerFormRef = ref();

// 默认维度列表
const defaultDimensions = ['尽责务实', '抗压能力', '客户导向', '学习能力', '逻辑分析', '分析思维'];

// 权重验证错误
const weightValidationErrors = ref<string[]>([]);

// 获取默认数据
function getDefaultData(): KeyPotentialQualityItem[] {
    return [
        {
            headName: '人均平均水平',
            rowDataList: defaultDimensions.map((dimension) => ({
                encDimensionId: '',
                dimensionName: dimension,
                define: '',
                weight: 0,
                normalAverageScore: 0,
                normalStandardDeviation: 0,
            })),
        },
        {
            headName: '服务人员',
            rowDataList: defaultDimensions.map((dimension) => ({
                encDimensionId: '',
                dimensionName: dimension,
                define: '',
                weight: 0,
                normalAverageScore: 0,
                normalStandardDeviation: 0,
            })),
        },
    ];
}

// 初始化数据
const internalData = ref<KeyPotentialQualityItem[]>(props.modelValue && props.modelValue.length > 0 ? [...props.modelValue] : getDefaultData());

// 动态列配置
const dynamicColumns = computed(() => {
    const columns: any[] = [
        {
            label: '维度',
            field: 'dimensionName',
            width: 120,
            fixed: 'left',
        },
    ];

    internalData.value.forEach((item) => {
        columns.push({
            label: item.headName,
            field: item.headName,
            width: 300,
            align: 'center',
        });
    });

    columns.push({
        label: '操作',
        field: 'operation',
        width: 100,
        fixed: 'right',
    });

    return columns;
});

// 转换为表格显示格式
const tableDisplayData = computed(() => {
    return defaultDimensions.map((dimension) => {
        const row: any = {
            dimensionName: dimension,
            headName: internalData.value[0]?.headName || '',
        };

        internalData.value.forEach((item) => {
            // eslint-disable-next-line max-nested-callbacks
            const rowData = item.rowDataList.find((r) => r.dimensionName === dimension);
            row[item.headName] = rowData || {
                encDimensionId: '',
                dimensionName: dimension,
                define: '',
                weight: 0,
                normalAverageScore: 0,
                normalStandardDeviation: 0,
            };
        });

        return row;
    });
});

// 添加表头
function addColumn() {
    headerForm.value.name = '';
    showAddHeaderDialog.value = true;
}

// 确认添加表头
async function confirmAddHeader() {
    const valid = await headerFormRef.value.validate();
    if (!valid) return;

    const newHeader = headerForm.value.name.trim();

    internalData.value.push({
        headName: newHeader,
        rowDataList: defaultDimensions.map((dimension) => ({
            encDimensionId: '',
            dimensionName: dimension,
            define: '',
            weight: 0,
            normalAverageScore: 0,
            normalStandardDeviation: 0,
        })),
    });

    showAddHeaderDialog.value = false;
    updateData();
    Toast.success('表头添加成功');
}

// 取消添加表头
function cancelAddHeader() {
    showAddHeaderDialog.value = false;
}

// 删除表头
function removeColumn(headName: string) {
    if (internalData.value.length <= 1) {
        Toast.danger('至少需要保留一个表头');
        return;
    }

    const index = internalData.value.findIndex((item) => item.headName === headName);
    if (index !== -1) {
        internalData.value.splice(index, 1);
        updateData();
        Toast.success('表头删除成功');
    }
}

// 验证表头名称
function validateHeaderName(value: string, callback: (message?: string) => void) {
    if (internalData.value.some((item) => item.headName === value.trim())) {
        callback('表头名称已存在');
        return;
    }
    callback();
}

// 验证权重总和
function validateWeight(headName: string) {
    const item = internalData.value.find((i) => i.headName === headName);
    if (!item) return;

    let totalWeight = 0;
    item.rowDataList.forEach((row) => {
        totalWeight += row.weight || 0;
    });

    const errorIndex = weightValidationErrors.value.findIndex((error) => error.includes(headName));

    if (Math.abs(totalWeight - 1) > 0.01) {
        const errorMsg = `${headName}列权重总和为${totalWeight.toFixed(2)}，应为1.0`;
        if (errorIndex === -1) {
            weightValidationErrors.value.push(errorMsg);
        } else {
            weightValidationErrors.value[errorIndex] = errorMsg;
        }
    } else {
        if (errorIndex !== -1) {
            weightValidationErrors.value.splice(errorIndex, 1);
        }
    }

    updateData();
}

// 更新数据
function updateData() {
    emit('update:modelValue', [...internalData.value]);
}

// 监听外部数据变化
watch(
    () => props.modelValue,
    (newValue) => {
        if (newValue) {
            // 如果有数据，直接使用
            if (newValue.length > 0) {
                internalData.value = [...newValue];
            } else {
                // 如果是空数组，重置为默认数据
                internalData.value = getDefaultData();
            }
        }
    },
    { deep: true, immediate: true }
);
</script>

<style lang="less" scoped>
.key-potential-quality-table-config {
    .config-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        h6 {
            margin: 0;
            font-size: 13px;
            font-weight: 500;
            color: #1d2129;
        }
    }

    .config-table-wrapper {
        margin-bottom: 16px;
    }

    .editable-cell {
        padding: 8px;

        .cell-group {
            display: flex;
            align-items: center;
            margin-bottom: 8px;

            &:last-child {
                margin-bottom: 0;
            }

            label {
                width: 80px;
                font-size: 12px;
                color: #86909c;
                margin-right: 8px;
                flex-shrink: 0;
            }
        }
    }

    .validation-errors {
        padding: 12px;
        background: #fff2f0;
        border: 1px solid #ffccc7;
        border-radius: 4px;

        .error-item {
            display: flex;
            align-items: center;
            color: #ff4d4f;
            font-size: 12px;
            margin-bottom: 4px;

            &:last-child {
                margin-bottom: 0;
            }

            .b-icon {
                margin-right: 4px;
            }
        }
    }
}
</style>
